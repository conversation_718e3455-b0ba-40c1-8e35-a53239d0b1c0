<template>
	<div :style="PageRef.PageStyle">
		<ComponentMgr v-for="(xItem,i) in PageRef.PageDom" :key="i"  :DomItem="xItem" :PathPrefix="'PageDom.' + i"/>
		
	    <div class="popup-container" v-if="PageRef.PagePopup.show" @click.stop="closePopup()">
			<div  class="popup-win-bottom" :style="compData.popupStyle" @click.stop="noneClick()">
				 <ComponentMgr v-for="(nItem,n) in PageRef.PagePopup.Doms" :key="n"  :DomItem="nItem" :PathPrefix="'PagePopup.Doms.' + n"/>
			 </div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,ref,onMounted,onUnmounted,provide,toRaw } from 'vue';
	import  ComponentMgr from './ComponentMgr.vue';
	import { getHomePage,getPage,getPart,getScript } from '../axios/dao/api.js';
	import { useRoute,useRouter } from 'vue-router'
	import { useLocalStorage,useGlobalData ,usePageParam} from '../pinia/globalData';
	import { useComponent } from '../frame/useComponent';
	import { xSysFunc } from '../frame/common';
	import { scpt } from '../frame/scpt.js';

	const route = useRoute()
	const router = useRouter()
	const storeage = useLocalStorage()
	const global = useGlobalData()
	const pageParam = usePageParam()
	
	const props = defineProps({
		PageContainName:{
			type:String,
			default:""
		}
	})
	
	const PageRef = reactive({
		PageId: 0, //页面Id
		PageName:"",
		PagePath:"",
		PageVersion:'0', //页面版本
		PageStyle: {}, // 页面样式
		PageCss: {}, //页面CSS
		PageDom: [], //面页组件数据
		PageData: {}, // 页面数据
		PageComponents:{},
		PageEvents: {}, // 页面事件配置
		CpnMap:new Map(), //所有组的ID与路径对应表  创建时自动产生
		PageParam:{}, //页面传入参数  页面传入
		PageContainer:"", //页面名称  挂载页面
		PageMaxId:0, //当前所有组件最大Id,
		PagePopup:{show:false,mode:'bottom',expose:0.5, anim:false,Doms:[] },
		
		loadPartJson,
		openPage,
		loadScript,
		openPopup,
		closePopup,
	})
	const compData = reactive({
		checkOpenPageStatus:false,
		popupStyle:{} as Record<string, string> //用动态控制弹窗动画样
	})
	provide("PageRef",PageRef)


	async function initPageJson(data:object){
		let xParam = {...data as any}
		
		if(props.PageContainName == "home"){
			if(!xParam.hasOwnProperty('pageId')) xParam['pageId'] = 0
			if(!xParam.hasOwnProperty('pagePath')) xParam['pagePath'] = ''
				
			let res = await getHomePage()
			
			if(res['code']==200){
				
				loadPageJson(res.data.Page)
				global.setSession(res.data.sessionId)
				storeage.setItem("page_home",res.data.Page)
				
				// 存储全局变量到shareData
				if(res.data.hasOwnProperty('globalVars')) {
					for(const key in res.data.globalVars) {
						global.setGlobalData(key, res.data.globalVars[key])
					}
				}
			}
		}else{
			const requestParams = {
				fileId: xParam.hasOwnProperty('pageId') ? xParam.pageId : null,
				path: xParam.hasOwnProperty('pagePath') ? xParam.pagePath : ''
			}
    
   		 	let res = await getPage(requestParams)
			if(res['code'] == 200){
				loadPageJson(res.data)
				storeage.setItem("page_"+res.data['PageId'],res.data)
				
				// 存储全局变量到shareData
				if(res.data.hasOwnProperty('globalVars')) {
					for(const key in res.data.globalVars) {
						global.setGlobalData(key, res.data.globalVars[key])
					}
				}
			}
		}
	}
	
	function loadPageJson(jsonData:object){
		PageRef.PageId = jsonData['PageId']
		PageRef.PageName = jsonData['PageName']
		PageRef.PagePath = jsonData['PagePath']
		PageRef.PageStyle = jsonData['PageStyle']
		PageRef.PageCss = jsonData['PageCss']
		PageRef.PageDom = jsonData['PageDom']
		PageRef.PageData = jsonData['PageData']
		PageRef.PageComponents = jsonData['PageComponents']
		PageRef.PageEvents = jsonData['PageEvents'] || {}
	}
	
	onMounted(async ()=>{

	  let pageKey = route.query.hasOwnProperty('pageKey') ? route.query.pageKey : ''
	  let param = pageParam.getPageParam(pageKey)
console.log("pageKey="+pageKey+",param="+JSON.stringify(param),'props.PageContainName='+props.PageContainName)
	  if(param == null) param = {}
	  const pageId = param.hasOwnProperty('pageId') ? param.pageId : 0
	  const pagePath = param.hasOwnProperty('pagePath') ? param.pagePath : ''

	  if(props.PageContainName != "home" && pageId == 0 && pagePath == ''){
		  xSysFunc.showDialog("页面参数错误，页面Id为空~",'页面打开错误','确定','')
		  router.back()
		  return
	  }
	  // 检查导航类型，判断是否是回退访问
	  const navigationType = storeage.getItem("page_navigation_type")
	  const isBackNavigation = navigationType !== "forward"
	  console.log("导航类型:", navigationType, "是否回退:", isBackNavigation)

	  // 清除导航类型标记，下次访问默认为回退
	  storeage.removeItem("page_navigation_type")

	  let hasCache = false
	  if(props.PageContainName == "home" || (props.PageContainName != "home" &&  pageId != 0 )){
		  let PageJson = storeage.getItem(props.PageContainName == "home" ? "page_home" : "page_"+pageId)
console.log("PageJson="+JSON.stringify(PageJson) )

		  if(PageJson != null && typeof PageJson=='object'){
			  loadPageJson(PageJson)
			  if(PageJson.PageVersion) param.version = PageJson.PageVersion
			  hasCache = true
		  }
	  }

	  // 只有在回退访问且有缓存时才跳过服务器请求，否则都从服务器获取最新数据
	  if(!(isBackNavigation && hasCache)){
		  await initPageJson(param)
	  }

	  // 页面加载完成后，检查是否有页面级别的 onMounted 事件配置
	  if(PageRef.PageEvents && PageRef.PageEvents['onMounted'] ) {
		  executePageScript(PageRef.PageEvents['onMounted'].fileId, {})
	  }
	})

	onUnmounted(()=>{
	  // 页面卸载时，检查是否有页面级别的 onUnmounted 事件配置
	  if(PageRef.PageEvents && PageRef.PageEvents['onUnMounted']) {
		  executePageScript(PageRef.PageEvents['onUnMounted'].fileId, {})
	  }
	})

	async function loadPartJson(partId:any,param:object,callback){
		let res = await getPart({
			fileId:partId,
			...param
		})
		if(callback){
			callback(res)
		}
	}
	
	async function loadScript(fileId,param,callback){
		console.log('loadScript',{fileId,...param});
		let res = await getScript({
			fileId:fileId,
			...param
		})
		if(callback){
			callback(res)
		}
	}

	// 页面级别的脚本执行函数
	function executePageScript(eventId, param = {}) {
		loadScript(eventId, param, (res) => {
			if(res.code == 200) {
				let pmObj = {}
				if(res.data.hasOwnProperty('param')) {
					for(let cname in res.data.param) {
						if(param.hasOwnProperty(cname)) {
							pmObj[cname] = param[cname]
						} else {
							pmObj[cname] = res.data.param[cname]
						}
					}
				}

				let context = {
					floor: 1,
					PageRef,
					blockMap: new Map,
					strMap: new Map,
					param: pmObj,
					doServer: {}, // 简化版本，页面级别可能不需要所有的 doServer 功能
					localVar: {},
					forBody: false,
					parent: null,
					scriptList: [],
					currentRow: 0,
					reverseRun: false
				}

				console.log("start run page script:")
				let lng = new Date().getTime()
				try {
					scpt.parse(context, res.data.script)
				} catch(error) {
					if(error.hasOwnProperty('category') && error['category'] == 'return') {
						console.log("Return Success the Result=" + JSON.stringify(error.result))
					} else if(error.hasOwnProperty('category') && error['category'] == 'callBack') {
						console.log("call over")
					} else {
						console.log("Error: " + error.message)
						xSysFunc.showDialog("Error: " + error.message, "脚本错误", '取消', "")
					}
				} finally {
					let costTime = (new Date().getTime() - lng) + 'ms'
					console.log('page script cost time = ' + costTime)
				}
			}
		})
	}
	
	function openPage(param:object){

		// 跳转前保存当前页面的最新数据（包含用户修改）
		const currentPageData = {
			PageId: PageRef.PageId,
			PageName: PageRef.PageName,
			PagePath: PageRef.PagePath,
			PageVersion: PageRef.PageVersion,
			PageStyle: PageRef.PageStyle,
			PageCss: PageRef.PageCss,
			PageDom: PageRef.PageDom,
			PageData: PageRef.PageData,
			PageComponents: PageRef.PageComponents,
			PageEvents: PageRef.PageEvents
		}
		const storageKey = props.PageContainName == "home" ? "page_home" : "page_" + PageRef.PageId
		storeage.setItem(storageKey, currentPageData)
		console.log("跳转前保存当前页面数据:", storageKey)

		// 标记这是一次新的页面跳转（非回退）
		storeage.setItem("page_navigation_type", "forward")

		let PageName = "Pagex1"
		if(props.PageContainName=='home'){
			PageName = "Pagex1"
		}else if(props.PageContainName=='pagex1'){
			PageName = "Pagex2"
		}else if(props.PageContainName=='pagex2'){
			PageName = "Pagex3"
		}else if(props.PageContainName=='pagex3'){
			PageName = "Pagex4"
		}else if(props.PageContainName=='pagex4'){
			PageName = "Pagex5"
		}else if(props.PageContainName=='pagex5'){
			PageName = "Pagex6"
		}else if(props.PageContainName=='pagex6'){
			PageName = "Pagex7"
		}else if(props.PageContainName=='pagex7'){
			PageName = "Pagex8"
		}else if(props.PageContainName=='pagex8'){
			PageName = "Pagex9"
		}else if(props.PageContainName=='pagex9'){
			PageName = "Pagex10"
		}else if(props.PageContainName=='pagex10'){
			PageName = "Pagex1"
		}
		let pageKey = pageParam.setPageParam(param)
		
		router.push({ name:PageName,query:{pageKey:pageKey}})
	}
	
	function noneClick(){} //拦截点击事件
	
	function openPopup(mode:string,expose:number,anim:boolean){
		if('bottom,top,left,right,center'.indexOf(mode) < 0){
			mode = 'bottom'
		}
		if(expose <= 0 || expose > 1) expose = 0.5
		PageRef.PagePopup.mode = mode 
		PageRef.PagePopup.expose = expose 
		PageRef.PagePopup.anim = ((''+anim)=='true')
		compData.popupStyle = {}
		if(PageRef.PagePopup.mode=='bottom'){
			compData.popupStyle.height = 'calc('+(PageRef.PagePopup.expose * 100)+'vh)'
			compData.popupStyle.left = '0px'
			compData.popupStyle.right = '0px'
			compData.popupStyle.borderRadius = '10px 10px 0px 0px'
			
			if(PageRef.PagePopup.anim){
				compData.popupStyle.bottom = 'calc('+(PageRef.PagePopup.expose * (-100))+'vh)'
			}else{
				compData.popupStyle.bottom = '0px'
			}
		}else if(PageRef.PagePopup.mode=='left'){
			compData.popupStyle.width = 'calc('+(PageRef.PagePopup.expose * 100)+'vw)'
			compData.popupStyle.top = '0px'
			compData.popupStyle.bottom = '0px'
			compData.popupStyle.borderRadius = '0px 10px 10px 0px'
			
			if(PageRef.PagePopup.anim){
				compData.popupStyle.left = 'calc('+(PageRef.PagePopup.expose * (-100))+'vw)'
			}else{
				compData.popupStyle.left = '0px'
			}
			
		}else if(PageRef.PagePopup.mode=='right'){
			compData.popupStyle.width = 'calc('+(PageRef.PagePopup.expose * 100)+'vw)'
			compData.popupStyle.top = '0px'
			compData.popupStyle.bottom = '0px'
			compData.popupStyle.borderRadius = '10px 0px 0px 10px'
			
			if(PageRef.PagePopup.anim){
				compData.popupStyle.left = '100vw'
			}else{
				compData.popupStyle.left = 'calc('+((1 - PageRef.PagePopup.expose) * 100)+'vw)'
			}
			
		}else if(PageRef.PagePopup.mode=='top'){
			compData.popupStyle.height = 'calc('+(PageRef.PagePopup.expose * 100)+'vh)'
			compData.popupStyle.left = '0px'
			compData.popupStyle.right = '0px'
			compData.popupStyle.borderRadius = '0px 0px 10px 10px'
			
			if(PageRef.PagePopup.anim){
				compData.popupStyle.top = 'calc('+(PageRef.PagePopup.expose * (-100))+'vh)'
			}else{
				compData.popupStyle.top = '0px'
			}
		}else{
			compData.popupStyle.height = 'calc('+(PageRef.PagePopup.expose * 100)+'vh)'
			compData.popupStyle.width = 'calc(100vw - 50px)'
			compData.popupStyle.top = 'calc('+(1 - PageRef.PagePopup.expose)*50+'vh)'
			compData.popupStyle.left = '25px'
			compData.popupStyle.right = '25px'
			compData.popupStyle.borderRadius = '10px 10px 10px 10px'
			if(PageRef.PagePopup.anim){
				compData.popupStyle.transform = 'scale(0.01)'
			}
		}
		PageRef.PagePopup.show = true
		if(PageRef.PagePopup.anim ){
			setTimeout(()=>{
				if(PageRef.PagePopup.mode=='bottom'){
					compData.popupStyle.bottom = '0px'
				}else if(PageRef.PagePopup.mode=='left'){
					compData.popupStyle.left = '0px'
				}else if(PageRef.PagePopup.mode=='right'){
					compData.popupStyle.left = 'calc('+((1 - PageRef.PagePopup.expose) * 100)+'vw)'
				}else if(PageRef.PagePopup.mode=='top'){
					compData.popupStyle.top = '0px'
				}else{
					compData.popupStyle.transform = 'scale(1)'
				}
			},10)
		}
	}
	
	function closePopup(){
		if(PageRef.PagePopup.mode=='bottom'){
			compData.popupStyle.bottom = 'calc('+(PageRef.PagePopup.expose * (-100))+'vh)'
		}else if(PageRef.PagePopup.mode=='left'){
			compData.popupStyle.left = 'calc('+(PageRef.PagePopup.expose * (-100))+'vw)'
		}else if(PageRef.PagePopup.mode=='right'){
			compData.popupStyle.left = '100vw'
		}else if(PageRef.PagePopup.mode=='top'){
			compData.popupStyle.top = 'calc('+(PageRef.PagePopup.expose * (-100))+'vh)'
		}else{
			compData.popupStyle.transform = 'scale(0.01)'
		}
		setTimeout(()=>{
			PageRef.PagePopup.show = false
			PageRef.PagePopup.Doms = []
		},500)
	}
	
</script>

<style lang="scss">
	.popup-container {
		  position: fixed;
		  top:0rpx;
		  left:0rpx;
		  width:100vw;
		  height:100vh;
		  background-color: rgba(91,91,91,0.5);
		  transition: all 0.5s;
		  z-index: 1000;
	}
	.popup-win-bottom {
		  position:absolute;
		  background-color:white;
		  bottom:0rpx;
		  left:0rpx;
		  right:0rpx;
		  transition: all 0.5s;
		  overflow: hidden;

	}
</style>