import { reactive,onMounted,ref,defineProps,inject, toRaw} from "vue";
import {xSysFunc } from '../frame/common.js'
import {scpt} from '../frame/scpt.js'
import { useGlobalData ,useLocalStorage } from '../pinia/globalData';
import { useRouter } from 'vue-router';

export function useComponent(props){
	const PageRef = inject('PageRef')
	const router = useRouter()
	
	onMounted(init)
	
	function init(){
		let idkey = "id_"+props.DomItem.id
		let idValue = props.PathPrefix
		if(PageRef.CpnMap.has(idkey)){
			let v = PageRef.CpnMap.get(idkey)
			if(v != idValue && props.DomItem.id > 0){
				console.log("组件Id有冲突，id="+idkey+",path="+v+",path2="+idValue)
			}
		}else{
			PageRef.CpnMap.set(idkey,idValue)
			let mVal = PageRef.CpnMap.get(idkey)
			// console.log("mVal="+ mVal+",mKey="+ idkey)
		}
		if(PageRef.xMaxId < parseInt(props.DomItem.id)){
			PageRef.xMaxId = parseInt(props.DomItem.id)
		}

		
		
		// console.log("kkkk",JSON.stringify(PageRef.CpnMap.keys()))
	}


	function getMaxComponentId() {
	  const currentMax = PageRef.xMaxId.value || 0;
	  if (currentMax > 0) return currentMax;
	
	  let maxId = 0;
	  const regex = /^id_(\d+)$/; // 正则匹配更安全
	  for (const [key, value] of PageRef.CpnMap.value) {
	    const match = key.match(regex);
	    if (match) {
	      const numId = parseInt(match[1], 10);
	      if (!isNaN(numId) && numId > maxId) {
	        maxId = numId;
	      }
	    }
	  }
	
	  // 根据业务需求调整初始值逻辑
	  const newMax = maxId > 0 ? maxId : 1001;
	  PageRef.xMaxId.value = newMax;
	  return newMax;
	}
	
	function findControlById(id){
		let idkey = "id_"+id
		if(PageRef.CpnMap.has(idkey)){
			return PageRef.CpnMap.get(idkey)
		}else{
			return ""
		}
	}
	
	function xRefCss(refCss){ //取CSS值函数 *****
		if(typeof refCss == 'string' && refCss.substring(0,12)=='ref:PageCss.'){
			let cols = refCss.substring(12).split('.')
			let a = PageRef.PageCss
			for(let i=0;i<cols.length;i++){
				a = a[cols[i]]
			}
			return a
		}else{
			return refCss
		}
	}
	
		function nCalc(s){
		
	  return	nCalc(nCalc(s, new Map))
	}
	function nCalc(s,mp){  //表达式计算函数 *****
		xSysFunc.sumFuncTime("comNCalc")
		if((s.indexOf('(') >= 0 && s.indexOf(')') < 0) || (s.indexOf('(') < 0 && s.indexOf(')') > 0)){
			console.log("对不起，表达式错误,缺少了一个括号 = " + s  )
			return s
		}
		if(s.indexOf('(') >= 0 && s.indexOf(')' > s.indexOf('('))){
			let sou = s
			let p = sou
			for(let n=0;n<100;n++){
				if(p.indexOf('(') >= 0 && p.indexOf(')' > p.indexOf('('))){
					p = p.substring(0,p.indexOf(')'))
					p = p.substring(p.lastIndexOf('(')+1)
					let v = nCalc(p,mp)
					sou = sou.replace('('+p+')',v)
					p = sou
				}else{
					return nCalc(p,mp)
				}
			}
		}else if(s.indexOf('+') > 0 ){
			let addArr = s.split('+')
			let checkNum = 'true'
			for(let ai=0;ai<addArr.length;ai++){
				if (xSysFunc.isNumeric(addArr[ai])){
					addArr[ai] = Number(addArr[ai])
				} else {
					addArr[ai] = nCalc(addArr[ai].trim(),mp)
				}
				
				if(!xSysFunc.isNumeric(addArr[ai])) checkNum = 'false'
			}
			let res = 0
			let res2 = ''
			for(let i2=0;i2<addArr.length;i2++){
				if(checkNum=='true'){
					res += parseFloat(addArr[i2])
				}else{
					res2 += xSysFunc.clearAA(addArr[i2])
				}
			}
			return checkNum == 'true' ? res : res2 
			
		}else if(s.indexOf(' - ') > 0 ){
			let reducArr = s.split(' - ')
			let res = 0
			for(let ai=0;ai<reducArr.length;ai++){
				let mv = nCalc(reducArr[ai].trim(),mp)
				if(xSysFunc.isNumeric(mv)){
					if(ai==0){
					res = parseFloat(mv)
					}else{
						res -= parseFloat(mv)
					}
				}else{
					console.log('表达式有错误，‘减号’ 后面不是数字：' + mv)
					return 'Err'
				}
			}
			return res
		}else if(s.indexOf('*') > 0){
			let sArr = s.split('*')
			let res = 0
			for(let ai=0;ai<sArr.length;ai++){
				let mv = nCalc(sArr[ai].trim(),mp)
				if(xSysFunc.isNumeric(mv)){
					if(ai==0){
					  res = parseFloat(mv)
					}else{
						res *= parseFloat(mv)
					}
				}else{
					console.log('表达式有错误，‘剩号’ 对象不是数字：' + mv)
					return 'Err'
				}
			}
			return res
		}else if(s.indexOf('/') > 0){
			let sArr = s.split('/')
			let res = 0
			for(let ai=0;ai<sArr.length;ai++){
				let mv = nCalc(sArr[ai].trim(),mp)
				if(xSysFunc.isNumeric(mv)){
					if(ai==0){
					  res = parseFloat(mv)
					}else{
						let cp = parseFloat(mv)
						if(cp == 0){
							console.log('表达式有错误，除数不能为0：' + sArr[ai].trim())
							return "Err"
						}
						res /= cp
					}
				}else{
					console.log('表达式有错误，‘除号’ 对象不是数字：' + mv)
					return 'Err'
				}
			}
			return res
		}else if(s.substring(0,5)=='item:' || s.substring(0,4)=='ref:'){
			return xRefValue(s)
		}else{

			if (typeof s === 'string' ) {
				if (!xSysFunc.isNumeric(s) && mp && mp.has(s)){
					return mp.get(s);
				} 	
			} 
			return s
		}
	}
		
	function doFunc(s,mp){
		xSysFunc.sumFuncTime("comDoFunc")
		let lastStr = s
		for(let j=0;j<100;j++){
			let xFunc = xSysFunc.findMeth(lastStr)
			if(xFunc.length > 0){
				let param = xSysFunc.getMathParam(xFunc)
				let param2 = doFunc(param,mp)
				let param3 = param2.split(',')
				for(let k=0;k<param3.length;k++){
					if(!xSysFunc.isNumeric(param3[k])){
						param3[k] = xCalc(param3[k])
					}
				}
				// let xFunc2 = xFunc.split(param).join(param3.join(','))
				let xFunc2 = xFunc.replaceAll(param,param3.join(','))
				let val = xSysFunc.matchMeth(xFunc2,mp)
				lastStr = lastStr.split(xFunc).join(val)
			}else{
				break
			}
		}
		return lastStr
	}
		
	function xCalc(s){
		return xxCalc(s,new Map)
	}
		
	function xxCalc(s,mp){
		xSysFunc.sumFuncTime("comXxCalc")
		if(s.indexOf('&&') > 0){
			let tmpBols = s.split('&&')
			if(tmpBols.length > 1){
				for(let nc=0;nc<tmpBols.length;nc++){
					let retV = xxCalc(tmpBols[nc],mp)
					if((typeof retV=='boolean' && retV==false) || (typeof retV=='string' && retV=='false')) return false
				}
				return true
			}
		}
		
		
		let sc = s.substring(0,5)=='calc:' ? s.substring(5) : s
		for(let n=0;n<100;n++){
			if(sc.indexOf("'") >= 0 && sc.substring(sc.indexOf("'")).indexOf("'") >= 0){
				let key = '@@'+n + '@@'
				let start = sc.indexOf("'")
				let end = start + sc.substring(start+1).indexOf("'")+2
				let v = sc.substring(start,end)
				sc = sc.split(v).join(key)
				mp.set(key,xSysFunc.clearAA(v))
			}else{
				break
			}
		}
		sc = doFunc(sc,mp) //函数处理
		let meq = ""
		if(sc.indexOf('==') > 0){
			meq = '=='
		}else if(sc.indexOf('>=') > 0){
			meq = '>='
		}else if(sc.indexOf('<=') > 0){
			meq = '<='
		}else if(sc.indexOf('!=') > 0){
			meq = '!='
		}else if(sc.indexOf('>') > 0){
			meq = '>'
		}else if(sc.indexOf('<') > 0){
			meq = '<'
		}
		let res = sc 
		if(meq.length > 0){
			let ms = sc.split(meq)
			if(ms && ms.length == 2){
				let leftV = nCalc(ms[0],mp)
				if(mp.size > 0 && typeof leftV=='string' && leftV.includes('@@')){
					for(let m=0;m<mp.size;m++){
						let key = "@@" + m + "@@"
						if(leftV.indexOf(key) >=0){
							// leftV = leftV.split(key).join(mp.get(key))
							leftV = leftV.replaceAll(key,mp.get(key))
						}
					}
				}
				let rightV = nCalc(ms[1],mp)
				if(mp.size > 0 && typeof rightV == 'string' && rightV.includes('@@')){
					for(let m=0;m<mp.size;m++){
						let key = "@@" + m + "@@"
						if(rightV.indexOf(key) >=0){
							// rightV = rightV.split(key).join(mp.get(key))
							rightV = rightV.replaceAll(key,mp.get(key))
						}
					}
				}
				
				if(typeof leftV =='string') leftV = leftV.trim()
					if (xSysFunc.isNumeric(leftV)){
						leftV = Number(leftV)
					} 
				if(typeof rightV == 'string') rightV = rightV.trim()
					if (xSysFunc.isNumeric(rightV)){
						rightV = Number(rightV)
					} 
				if(meq  == '=='){
					return leftV == rightV
				}else if(meq=='!='){
					return leftV != rightV
				}else if(meq=='>='){
					return leftV >= rightV
				}else if(meq=='<='){
					return leftV <= rightV 
				}else if(meq=='<'){
					return leftV < rightV
				}else if(meq=='>'){
					return leftV > rightV 
				}else{
					return false
				}
				
			}else{
				console.log("表达式错误")
			}
		}else{
			res = nCalc(sc,mp)
		}
		if(mp.size > 0 && typeof res == 'string' && res.length > 0 && res.indexOf('@@') >= 0){
			let firstArr = ('**'+res+'**').split('@@')
			if(firstArr.length > 2){
				for(let m=1;m<firstArr.length - 1;m++){
					if(firstArr[m].indexOf("#")){
						let secArr = firstArr[m].split('#')
						if(secArr.length == 2 && xSysFunc.isNumeric(secArr[0]) && xSysFunc.isNumeric(secArr[1])){
							let key = "@@" + firstArr[m] + "@@"
							if(mp.has(key)){
								res = res.replaceAll(key,mp.get(key))
							}
						}
					}
				}
			}
		}
		if(typeof res=='string' && !xSysFunc.isNumeric(res) && mp.has(res)){
			res = mp.get(res)
		}
		return res
	}

	function findComponentParams(cpId){
		xSysFunc.sumFuncTime("findComponentParams")

		if (cpId != '' && PageRef['PageComponents'] && PageRef['PageComponents'][cpId] && PageRef['PageComponents'][cpId].componentParams){
			return PageRef['PageComponents'][cpId].componentParams;
		} else {
			return []
		}
		
	}
	function findComponentVars(cpId){
		xSysFunc.sumFuncTime("findComponentVars") 
		if (cpId != '' && PageRef['PageComponents'] && PageRef['PageComponents'][cpId] && PageRef['PageComponents'][cpId].componentVars){
			return PageRef['PageComponents'][cpId].componentVars;
		} else {
			return []
		}
	}
	function findComponentId(path){

		xSysFunc.sumFuncTime("findComponentId")
		let id = ""
		if(typeof path == 'string' && path.substring(0,14) == 'ref:Component[' && path.indexOf('].') > 0) {
			id = path.substring(14,path.indexOf('].'))
			let key = path.substring(path.indexOf('].') + 2,)
		}
		return id
	}
	function findComponentVarName(path){

		xSysFunc.sumFuncTime("findComponentVarName")
		let key = ""
		if(typeof path == 'string' && path.substring(0,14) == 'ref:Component[' && path.indexOf('].') > 0) {
			
		 key = path.substring(path.indexOf('].') + 2,)
		}
		return key
	}
	function xComponentObj(path){
		
		const id = findComponentId(path)
		const key = findComponentVarName(path)
		const componentParams =  findComponentParams(id)
		const componentVars =  findComponentVars(id)
		return {
			id:id,
			key:key,
			componentVars:componentVars,
			componentParams:componentParams
		}
		
	}
		
	function xRefValue(refValue){ //取值函数  ******
		if(typeof refValue == 'string' && refValue.substring(0,5)=='calc:'){
			return  xCalc(refValue.substring(5))
		}else{
			if(typeof refValue == 'string' && refValue.substring(0,5)=='item:'){
				// 组件使用自己内部变量默认值，没有关联页面或全局变量时候
				const itemDataPath = props.ItemDataPath
				let value
				if(typeof itemDataPath == 'string' && itemDataPath.substring(0,14) == 'ref:Component[' && itemDataPath.indexOf('].') > 0) {
					const cpId = findComponentId(itemDataPath)
					const cols = itemDataPath.split('.') 
					const key = cols[1];
					const index = cols[2];
					const itemName = refValue.split(":")[1];
					const componentVars = findComponentVars(cpId)
					if(componentVars) {
						componentVars.forEach(e=>{
							if(e.name === key){
								
								const dataArray = e.default || e.value ;
								if(dataArray && dataArray[index]) {
									value = dataArray[index][itemName];
								}
								
							}
						})
					}
				} else {
					refValue = props.ItemDataPath + "."+refValue.substring(5)
				}
				if(value){
					return value
				}
				
			}
			if(typeof refValue == 'string' && refValue.substring(0,13)=='ref:PageData.'){
				let cols = refValue.substring(13).split('.')
				let a = PageRef.PageData
				for(let i=0;i<cols.length;i++){
					a = a[cols[i]]
				}
				return a
			}else if(typeof refValue == 'string' && refValue.substring(0,14)=='ref:PageParam.'){
				let cols = refValue.substring(14).split('.')
				let a = PageRef.PageParam
				for(let i=0;i<cols.length;i++){
					a = a[cols[i]]
				}
				return a
			}else if(typeof refValue == 'string' && refValue.substring(0,12)=='ref:PageDom.'){
				if(refValue.indexOf(':PageDom.[') > 0 && refValue.indexOf('].') > 0){
					refValue = xGetSourceValue(refValue)		
				}
				let cols = refValue.substring(12).split('.')
				let a = PageRef.PageDom
				for(let i=0;i<cols.length;i++){
					a = a[cols[i]]
				}
				return a
			}else if(typeof refValue == 'string' && refValue.substring(0,15)=='ref:GlobalData.'){
				let cols = refValue.substring(15).split('.')
				let a = useGlobalData().getGlobalData(cols[0])
				for(let i=1; i<cols.length; i++){
					if(a) a = a[cols[i]]
				}
				return a
			}else if(typeof refValue == 'string' && refValue.substring(0,6)=='local:'){
				return "../../static/" + refValue.substring(6)
			}else if(typeof refValue == 'string' && refValue.substring(0,14) == 'ref:PopupDoms.'){
				if(refValue.indexOf(':PopupDoms.[') > 0 && refValue.indexOf('].') > 0){
					refValue = xGetSourceValue(refValue)		
				}
				let cols = refValue.substring(12).split('.')
				let a = PageRef.PagePopup.Doms
				for(let i=0;i<cols.length;i++){
					a = a[cols[i]]
				}
				return a
			
			}else if(typeof refValue == 'string' && refValue.substring(0,14) == 'ref:Component['){
                // PageRef.PageComponets.xxxxx.ComponentParams
                // PageRef.PageComponets.xxxxx.ComponentVars
                //TODO // "ref:Component[1753080909336].image"
                let value;
                if(refValue.indexOf('].') > 0){
                    let cpId = refValue.substring(14,refValue.indexOf('].'))
                    
                    let key = refValue.substring(refValue.indexOf('].') + 2,)
                    let componentParams;
                    let componentVars;
                    if(PageRef.PageComponents && PageRef.PageComponents[cpId]) {
                        
                        if (PageRef.PageComponents[cpId].componentParams){
                            componentParams = PageRef.PageComponents[cpId].componentParams;
                        }
                            // 组件内部自有参数引用解析
                        if (PageRef.PageComponents[cpId].componentVars){
                            componentVars = PageRef.PageComponents[cpId].componentVars;
                            componentVars.forEach(element => {
                                if(element.name === key){
                                    if ( typeof element.value == 'string' && element.value.substring(0,13) == 'ref:Component'){
                                        // 引用组件页面给组件传递了参数
                                        if (componentParams){
                                            componentParams.forEach(param => {
                                                if (param.name === key){
                                                    if (param.expression && param.expression.substring(0,4) == 'ref:'){
                                                        console.log(key,':使用页面引用值：',xRefValue(param.expression));
                                                        value = xRefValue(param.expression)
                                                    } else {
                                                        console.log(key,':使用页面输入值：',param.value);
                                                        value = xRefValue(param.value);
                                                    }
                                                } 
                                            });
                                        } else {
                                            console.log('未匹配到页面参数数组')
                                            value = element.value
                                        }
                                    } else {
                                        value = xRefValue(element.value)
                                        
                                    }
                                    
                                }

                            });
                        } else {
                            console.log('未匹配到组件参数数组')
                        }    
                    }
                    return value
                }
            }else{
                return refValue
            }
		}
	}
		
	function xGetSourceValue(rfValue){
		
		if(typeof rfValue == 'string' && rfValue.substring(0,12)=='ref:PageDom.'){
			let s = rfValue
			let n = s.indexOf(']')
			let id = s.substring(13,n)
			let ms = findControlById(id)
			
			if(ms != ""){
				return "ref:"+ms+s.substring(n + 1)
			}else{
				return rfValue
			}
		}else if(typeof rfValue == 'string' && rfValue.substring(0,14)=='ref:PopupDoms.'){
			let s = rfValue
			let n = s.indexOf(']')
			let id = s.substring(15,n)
			let ms = findControlById(id)
			if(ms != ""){
				return "ref:"+ms+s.substring(n + 1)
			}else{
				return rfValue
			}
		}else{
			return rfValue
		}
	}
	
	function xSetValue(xColumn, xValue) {
		if (typeof xColumn == 'string' && xColumn.substring(0, 13) == 'ref:PageData.') {
			xSysFunc.itemSet(PageRef.PageData,xColumn.substring(13),xValue)
			
		}else if (typeof xColumn == 'string' && xColumn.substring(0, 12) == 'ref:PageDom.') {
			if(xColumn.indexOf(':PageDom.[') > 0 && xColumn.indexOf('].') > 0){
				xColumn = xGetSourceValue(xColumn)		
			}
			xSysFunc.itemSet(PageRef.PageDom,xColumn.substring(12),xValue)
			
		}else if (typeof xColumn == 'string' && xColumn.substring(0, 15) == 'ref:GlobalData.') {
			xSysFunc.itemSet(useGlobalData().getGlobalData(xColumn.substring(15)),xColumn.substring(15),xValue)	
		}else if(typeof xColumn == 'string' && xColumn.substring(0,14) == 'ref:Component[' && xColumn.indexOf('].') > 0) {
			let cpId = findComponentId(xColumn)
			let key = findComponentVarName(xColumn)
			let componentParams = findComponentParams(cpId)
			let componentVars = findComponentVars(cpId)
			if(componentParams) {
				componentParams.forEach(e=>{
					if(e.name === key){
						if (e.expression && e.expression.substring(0,4) === 'ref:'){
 							xSetValue(e.expression, xValue)
						} else {
							e.value = xValue
						}
						
					}
				})
			} else if(componentVars) {
				componentVars.forEach(e=>{
					if(e.name === key){
						if (e.expression && e.expression.substring(0,4) === 'ref:'){
 							xSetValue(e.expression, xValue)
						} else {
							e.value = xValue
						}
						
					}
				})
			}
		}
	}
		
	function xSetChildren(xComp,children,mode){
		console.log("xSetChildren",xComp,children,mode)
		if (typeof xComp == 'string' && xComp.substring(0, 12) == 'ref:PageDom.') {

			if(xComp.indexOf(':PageDom.[') > 0 && xComp.indexOf('].') > 0){
				xComp = xGetSourceValue(xComp)
			}

			xSysFunc.domSet(PageRef.PageDom,xComp.substring(12),children,mode,PageRef)
		}
	}

	function xOnDbClick(pmSource){
		let clickParam = toRaw(pmSource)
		let params = clickParam.hasOwnProperty('params') ? clickParam.params : {}
		console.log("双击事件",clickParam)
		if(params && params.size > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(clickParam.fileId,params)
			
	}
		
	function xOnPageNoChange(pmSource){
		let clickParam = toRaw(pmSource)
		let params = clickParam.hasOwnProperty('params') ? clickParam.params : {}

		if(params && params.length > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(clickParam.fileId,params)
			
	}

	function xonChanged(pmSource){
		let clickParam = toRaw(pmSource)
		let params = clickParam.hasOwnProperty('params') ? clickParam.params : {}
		if(params && params.size > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(clickParam.fileId,params)
	}

	function xOnFocus(pmSource){
		let clickParam = toRaw(pmSource)
		let params = clickParam.hasOwnProperty('params') ? clickParam.params : {}
		if(params && params.size > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(clickParam.fileId,params)
	}

	function xOnBlur(pmSource){
		let clickParam = toRaw(pmSource)
		let params = clickParam.hasOwnProperty('params') ? clickParam.params : {}
		if(params && params.size > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(clickParam.fileId,params)
	}

	function xOnMounted(pmSource){
		let pageParam = toRaw(pmSource)
		let params = pageParam.hasOwnProperty('params') ? pageParam.params : {}
		if(params && params.size > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(pageParam.fileId,params)
	}

	function xOnUnMounted(pmSource){
		let pageParam = toRaw(pmSource)
		let params = pageParam.hasOwnProperty('params') ? pageParam.params : {}
		if(params && params.size > 0){
			for(let key in params){
				params[key] = xRefValue(params[key])
			}
		}
		loadScriptJson(pageParam.fileId,params)
	}

	function xOnClick(pmSource){
		let clickParam = toRaw(pmSource)
		if(clickParam){
			let param = clickParam.hasOwnProperty('param') ? clickParam.param : {}
			if(clickParam.type=='event' && clickParam.hasOwnProperty('eventId')){
				 if(param && param.size > 0){
					 for(let key in param){
						 param[key] = xRefValue(param[key])
					 }
				 }
				 loadScriptJson(clickParam.eventId,param)
				 
			}else if(clickParam.type=='link'){
				if(clickParam.target=='page' && clickParam.hasOwnProperty('fileId')){
					let param1 = clickParam.hasOwnProperty('param') ? clickParam.param : ""
					
					PageRef.openPage({
						pageId: clickParam.fileId, 
						param: param1
					})
				  
				}else if(clickParam.target=='part'){

					if(clickParam.hasOwnProperty('fileId')){
						PageRef.loadPartJson(clickParam.fileId,param,(res)=>{
							console.log('=== 加载部件数据 ===')
							console.log('部件数据:', res.data)

							// 1. 处理组件变量数据
							if(res.data.id && res.data.componentVars){
								console.log('添加组件变量到PageComponents:', res.data.id)

								// 创建组件对象
								let componentData = {
									componentVars: []
								}

								// 转换componentVars格式
								res.data.componentVars.forEach(varItem => {
									componentData.componentVars.push({
										name: varItem.name,
										value: varItem.value
									})
								})

								// 添加到PageComponents
								if(!PageRef.PageComponents){
									PageRef.PageComponents = {}
								}
								PageRef.PageComponents[res.data.id] = componentData

								console.log('PageComponents更新后:', PageRef.PageComponents)
							}

							// 2. 处理子组件DOM
							let childBox = "ref:PageDom.["+clickParam.parentName+"].children"
							xSetChildren(childBox,res.data,clickParam.mode)
						})
					}
				}else if(clickParam.target=='popup'){
					let direct = clickParam.hasOwnProperty('direct') ? clickParam.direct : 'bottom'
					let expose = clickParam.hasOwnProperty('expose') ? clickParam.expose : 0.6
					if(clickParam.hasOwnProperty('children') && clickParam.children.length > 0){
						PageRef.PagePopup.Doms = clickParam.children
						PageRef.openPopup(direct,expose,true)
					}else if(clickParam.hasOwnProperty('fileId')){
						PageRef.loadPartJson(clickParam.fileId,param,(res)=>{
							  PageRef.PagePopup.Doms = res.data.PageDom
							  PageRef.openPopup(direct,expose,true)
							  clickParam.children = res.data.PageDom
						})
					}
				}
				
			}else if(clickParam.type=='command'){
				if(clickParam.command=="close" || clickParam.command=='closePage'){
					// 标记这是一次回退导航
					const storage = useLocalStorage()
					storage.setItem("page_navigation_type", "back")
					router.back()
				}else if(clickParam.command == 'closePopup'){
					console.log("closePopup")
					PageRef.closePopup()

				}else if(clickParam.command=='test'){

				}
			}
		}
	}
		
	function loadScriptJson(eventId,param){
		let doServer = {xSetValue,xSetChildren,xRefValue,xCalc,xxCalc,xRefCss}
		PageRef.loadScript(eventId,param,(res)=>{
			if(res.code==200){
				let pmObj = {}
				if(res.data.hasOwnProperty('param')){
					for(let cname in res.data.param){
						if(param.hasOwnProperty(cname)){
							if(typeof param[cname] == 'string'){
								pmObj[cname] = xCalc(param[cname])
							}else{
								pmObj[cname] = param[cname]
							}
						}else{
							pmObj[cname] = res.data.param[cname]
						}
					}
				}
				let context = {
					floor:1,
					PageRef,
					blockMap:new Map,
					strMap:new Map,
					param:pmObj,
					doServer,
					localVar:{},
					forBody:false,
					parent:null,
					scriptList:[],
					currentRow:0,
					reverseRun:false
				}
				console.log("start run script:")
				let lng = new Date().getTime()
				try{
					scpt.parse(context,res.data.script)
					// 若是[obj:xxx]格式的字符串，转换为实际对象
					// for (const key in PageRef.PageData) {
					// 	if (typeof PageRef.PageData[key] === 'string' && 
					// 		PageRef.PageData[key].indexOf('[obj:') === 0 && 
					// 		PageRef.PageData[key].slice(-1) === ']') {
							
					// 		const varName = PageRef.PageData[key].substring(5, PageRef.PageData[key].length - 1);
							
					// 		if (context.localVar[varName] && context.localVar[varName].value) {
							
					// 			PageRef.PageData[key] = context.localVar[varName].value;
					// 			console.log(`自动转换变量: ${key} = `, context.localVar[varName].value);
					// 		}
					// 	}
					// }
				}catch(error){
					if(error.hasOwnProperty('category') && error['category']=='return'){
						console.log("Return Success the Result="+ JSON.stringify(error.result))
					}else if(error.hasOwnProperty('category') && error['category']=='callBack'){
						console.log("call over")
					}else{
					  console.log("Error: "+error.message)
					  xSysFunc.showDialog("Error: "+error.message,"脚本错误",'取消',"")
					}
				}finally{
					let costTime = (new Date().getTime() - lng) + 'ms'
					console.log('script cost time = '+ costTime)
				}
			}
		})
	}

	return {xRefCss,xOnClick,xOnDbClick,xOnPageNoChange,xonChanged,xOnFocus,xOnBlur,xOnMounted,xOnUnMounted,xRefValue,doFunc,xCalc,xxCalc,xSetValue,xSetChildren,PageRef,init,xComponentObj}
}